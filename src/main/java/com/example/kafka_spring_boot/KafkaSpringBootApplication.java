package com.example.kafka_spring_boot;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.kafka.annotation.EnableKafka;

@Service
class DebeziumEventListener {
    private static final Logger logger = LoggerFactory.getLogger(DebeziumEventListener.class);

    @KafkaListener(topics = "test-topic", groupId = "kafka-spring-boot")
    public void listen(String value,
        @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
        @Header(KafkaHeaders.RECEIVED_KEY) String key) {
        logger.info(String.format("Consumed event from topic %s: key = %-10s value = %s", topic, key, value));
    }
}

@SpringBootApplication
@EnableKafka
public class KafkaSpringBootApplication {

	private static final Logger logger = LoggerFactory.getLogger(KafkaSpringBootApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(KafkaSpringBootApplication.class, args);
    }

    @Bean
    public CommandLineRunner CommandLineRunnerBean() {
        return args -> {
			// Wait for the consumer to start
			logger.info("Kafka consumer is ready to receive messages...");
        };
    }
}
